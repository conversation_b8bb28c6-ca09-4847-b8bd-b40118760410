;root@vultr:~/supervisor# more /etc/supervisor/conf.d/python_19999_supervisor.conf 
[program:python_19999_supervisor] ;程序名称，终端控制时需要的标识，可以将文件命名为 python_19999_supervisor.conf
command=python3 -u -m http.server 19999 ; 运行程序的命令
;command=bash python_19999_supervisor.sh ; 运行程序的命令
directory=/root/aishuati-master ; 命令执行的目录
autorestart=true ; 程序意外退出是否自动重启
autostart=true ;startsecs=0 ;stopsignal=INT
stderr_logfile=/root/supervisor/test.err.log ; 错误日志文件
stdout_logfile=/root/supervisor/test.out.log ; 输出日志文件
user=root ; 进程执行的用户身份
