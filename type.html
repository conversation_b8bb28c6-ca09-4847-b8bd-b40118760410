<!--
 * @Author: mokevip
 * @Date: 2020-09-14 09:24:16
 * @LastEditors: mokevip
 * @LastEditTime: 2020-09-14 11:00:40
 * @Description: 
-->
<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Expires" content="0">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
        <meta http-equiv="Cache" content="no-cache">
        <title>答题类型选择</title>
        <link rel="stylesheet" href="./css/iview.css">
        <script>
        
    </script>
        <style>
        .typeList {
            width: 95vw;
            margin: 2vw auto;
        }
        
        .typeList>div {
            margin-top: 2vw;
        }

        .contilor{

width: 50%;

margin: 5% auto;

}

.box{

display: flex;

flex-wrap: wrap;

}

.item{
    font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #747677;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
}

.item1{
    font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #036232;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
}

.type {
            font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #1890ff;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
        }

        .field-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 8px;
            min-width: 150px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .field-card:hover {
            border-color: #1890ff;
            background: #f0f8ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .field-selected {
            border-color: #1890ff !important;
            background: #e6f7ff !important;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .field-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .field-count {
            color: #666;
            font-size: 0.8rem;
        }

    </style>

    </head>

    <body>
        <div id="el">
            <p style="margin-left:3vw;margin-top: 3vw;font-size: 0.9rem;">请选择答题模式：
                <label class='item' v-for="(item,i) in itemList" :key="i" :class="{'item1':item.select?true:false}" @click="onSelect1(i)">
                {{item.text}}
                </label>
            </p>  

            <div class="typeList">
                <div v-for="i in typeList" @click='goNext(i.id)'>
                    <card class="timu">
                        {{i.name}}
                    </card>
                </div>
            </div>
            
            <!-- 添加专业分类选择区域 -->
            <div v-if="showFieldSelector" style="margin: 20px 3vw;">
                <h3>请选择专业分类：</h3>
                <div style="margin-bottom: 20px;">
                    <p style="color: #666; font-size: 0.8rem;">
                        共找到 {{fieldList.length}} 个专业分类，点击选择后可进行该分类下的专项练习
                    </p>
                </div>
                <div style="display: flex; flex-wrap: wrap; margin-top: 10px;">
                    <div v-for="(fieldInfo, index) in fieldListWithCount" :key="index"
                         class="field-card"
                         :class="{'field-selected': selectedField === fieldInfo.name}"
                         @click="selectField(fieldInfo.name)">
                        <div class="field-name">{{fieldInfo.name}}</div>
                        <div class="field-count">{{fieldInfo.count}}题</div>
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <i-button @click="showFieldSelector = false" type="default">返回模式选择</i-button>
                    <i-button @click="goToFieldManager" type="primary" style="margin-left: 10px;">
                        📚 专业分类管理
                    </i-button>
                </div>
            </div>
        </div>
        <script src="./js/vue.min.js"></script>
        <script src="./js/iview.min.js"></script>
        <script>
        const vue = new Vue({
            el: "#el",
            data: {
                itemList : [{
                    id: '1',
                    text: '打乱选项',
                    select: parseInt(sessionStorage.shuf_option)
                }],
                typeList: [{
                    id: 'random',
                    name: "乱序答题"
                }, {
                    id: 'order',
                    name: "顺序答题"
                }, {
                    id: 'recite',
                    name: "背题模式"
                }, {
                    id: 'wrong',
                    name: "错题模式"
                }, {
                    id: 'field',
                    name: "按专业分类答题"
                }],
                showFieldSelector: false,
                fieldList: [],
                fieldListWithCount: [], // 带题目数量的分类列表
                selectedField: '',
                selectedMode: '', // 存储用户选择的模式（顺序、乱序、背题）
                allQuestions: [] // 存储所有题目数据
            },
            mounted() {
                // 页面加载时获取专业分类列表
                this.getFieldList();
            },
            methods: {
                goNext(id) {
                    if (id === 'field') {
                        // 如果选择了按专业分类答题，显示分类选择器
                        this.showFieldSelector = true;
                        return;
                    }
                    
                    sessionStorage.type = id;
                    sessionStorage.field = ''; // 清除之前可能存在的专业分类选择
                    
                    if (parseInt(sessionStorage.shuf_option)){
                        window.location.href = "./timu_s.html"; 
                        return;
                    }
                    window.location.href = "./timu.html";
                },
                onSelect1(i) {
                    if (this.itemList[i].select) {
                        this.itemList[i].select = 0
                    } else {
                        this.itemList[i].select = 1
                    }
                    sessionStorage.shuf_option = this.itemList[i].select;
                },
                // 获取专业分类列表
                getFieldList() {
                    const fileId = sessionStorage.id;
                    const fileName = sessionStorage.file;

                    // 从JSON文件中获取专业分类列表
                    axios.get('./json/' + fileName)
                        .then(response => {
                            const data = response.data;
                            this.allQuestions = data; // 保存所有题目数据

                            // 统计每个分类的题目数量
                            const fieldCount = {};
                            data.forEach(item => {
                                if (item.一级纲要 && item.一级纲要.trim() !== '') {
                                    const field = item.一级纲要.trim();
                                    fieldCount[field] = (fieldCount[field] || 0) + 1;
                                }
                            });

                            // 创建带数量的分类列表
                            this.fieldListWithCount = Object.entries(fieldCount)
                                .map(([name, count]) => ({ name, count }))
                                .sort((a, b) => b.count - a.count); // 按题目数量降序排列

                            this.fieldList = this.fieldListWithCount.map(item => item.name);
                        })
                        .catch(error => {
                            console.error('获取专业分类失败:', error);
                            this.$Message.error('获取专业分类失败');
                        });
                },
                // 选择专业分类
                selectField(field) {
                    this.selectedField = field;

                    // 获取该分类的题目数量
                    const fieldInfo = this.fieldListWithCount.find(item => item.name === field);
                    const questionCount = fieldInfo ? fieldInfo.count : 0;

                    // 显示模式选择
                    this.$Modal.confirm({
                        title: '选择答题模式',
                        content: `您已选择"${field}"分类（共${questionCount}题），请选择答题模式：`,
                        okText: '开始答题',
                        cancelText: '取消',
                        width: 500,
                        render: (h) => {
                            return h('div', { style: 'padding: 20px 0;' }, [
                                h('div', { style: 'margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;' }, [
                                    h('h4', { style: 'margin: 0 0 10px 0; color: #333;' }, '分类信息'),
                                    h('p', { style: 'margin: 5px 0; color: #666;' }, `📚 分类名称：${field}`),
                                    h('p', { style: 'margin: 5px 0; color: #666;' }, `📊 题目数量：${questionCount}题`),
                                    h('p', { style: 'margin: 5px 0; color: #666;' }, `🎯 专项练习：针对该分类进行集中训练`)
                                ]),
                                h('h4', { style: 'margin: 20px 0 15px 0; color: #333;' }, '选择答题模式：'),
                                h('RadioGroup', {
                                    props: {
                                        value: this.selectedMode
                                    },
                                    on: {
                                        'input': (value) => {
                                            this.selectedMode = value;
                                        }
                                    },
                                    style: 'display: flex; flex-direction: column; gap: 12px;'
                                }, [
                                    h('Radio', {
                                        props: { label: 'order' },
                                        style: 'padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; margin: 0;'
                                    }, [
                                        h('div', [
                                            h('strong', '📖 顺序答题'),
                                            h('div', { style: 'color: #666; font-size: 12px; margin-top: 4px;' }, '按题目顺序依次练习，适合系统学习')
                                        ])
                                    ]),
                                    h('Radio', {
                                        props: { label: 'random' },
                                        style: 'padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; margin: 0;'
                                    }, [
                                        h('div', [
                                            h('strong', '🔀 乱序答题'),
                                            h('div', { style: 'color: #666; font-size: 12px; margin-top: 4px;' }, '随机打乱题目顺序，增加练习难度')
                                        ])
                                    ]),
                                    h('Radio', {
                                        props: { label: 'recite' },
                                        style: 'padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; margin: 0;'
                                    }, [
                                        h('div', [
                                            h('strong', '📝 背题模式'),
                                            h('div', { style: 'color: #666; font-size: 12px; margin-top: 4px;' }, '直接显示答案和解析，适合快速记忆')
                                        ])
                                    ])
                                ])
                            ]);
                        },
                        onOk: () => {
                            if (!this.selectedMode) {
                                this.$Message.warning('请选择答题模式');
                                return false; // 阻止关闭对话框
                            }

                            // 设置专业分类和答题模式
                            sessionStorage.field = field;
                            sessionStorage.type = this.selectedMode;

                            // 显示开始提示
                            this.$Message.success(`开始${field}分类${this.getModeText(this.selectedMode)}，共${questionCount}题`);

                            // 延迟跳转，让用户看到提示
                            setTimeout(() => {
                                if (parseInt(sessionStorage.shuf_option)){
                                    window.location.href = "./timu_s.html";
                                } else {
                                    window.location.href = "./timu.html";
                                }
                            }, 1000);
                        }
                    });
                },

                // 获取模式文本
                getModeText(mode) {
                    const modeMap = {
                        'order': '顺序答题',
                        'random': '乱序答题',
                        'recite': '背题模式'
                    };
                    return modeMap[mode] || '答题';
                },

                // 跳转到专业分类管理页面
                goToFieldManager() {
                    window.location.href = "./field_manager.html";
                }
            }
        })
    </script>
    </body>

</html>
