<!--
 * @Author: mokevip
 * @Date: 2020-09-14 09:24:16
 * @LastEditors: mokevip
 * @LastEditTime: 2020-09-14 11:00:40
 * @Description: 
-->
<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Expires" content="0">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
        <meta http-equiv="Cache" content="no-cache">
        <title>答题类型选择</title>
        <link rel="stylesheet" href="./css/iview.css">
        <script>
        
    </script>
        <style>
        .typeList {
            width: 95vw;
            margin: 2vw auto;
        }
        
        .typeList>div {
            margin-top: 2vw;
        }

        .contilor{

width: 50%;

margin: 5% auto;

}

.box{

display: flex;

flex-wrap: wrap;

}

.item{
    font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #747677;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
}

.item1{
    font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #036232;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
}

.type {
            font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #1890ff;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
        }

    </style>

    </head>

    <body>
        <div id="el">
            <p style="margin-left:3vw;margin-top: 3vw;font-size: 0.9rem;">请选择答题模式：
                <label class='item' v-for="(item,i) in itemList" :key="i" :class="{'item1':item.select?true:false}" @click="onSelect1(i)">
                {{item.text}}
                </label>
            </p>  

            <div class="typeList">
                <div v-for="i in typeList" @click='goNext(i.id)'>
                    <card class="timu">
                        {{i.name}}
                    </card>
                </div>
            </div>
            
            <!-- 添加专业分类选择区域 -->
            <div v-if="showFieldSelector" style="margin: 20px 3vw;">
                <h3>请选择专业分类：</h3>
                <div style="display: flex; flex-wrap: wrap; margin-top: 10px;">
                    <label v-for="(field, index) in fieldList" :key="index" 
                           class="item" :class="{'item1': selectedField === field}"
                           style="margin: 5px; cursor: pointer;"
                           @click="selectField(field)">
                        {{field}}
                    </label>
                </div>
            </div>
        </div>
        <script src="./js/vue.min.js"></script>
        <script src="./js/iview.min.js"></script>
        <script>
        const vue = new Vue({
            el: "#el",
            data: {
                itemList : [{
                    id: '1',
                    text: '打乱选项',
                    select: parseInt(sessionStorage.shuf_option)
                }],
                typeList: [{
                    id: 'random',
                    name: "乱序答题"
                }, {
                    id: 'order',
                    name: "顺序答题"
                }, {
                    id: 'recite',
                    name: "背题模式"
                }, {
                    id: 'wrong',
                    name: "错题模式"
                }, {
                    id: 'field',
                    name: "按专业分类答题"
                }],
                showFieldSelector: false,
                fieldList: [],
                selectedField: '',
                selectedMode: '' // 存储用户选择的模式（顺序、乱序、背题）
            },
            mounted() {
                // 页面加载时获取专业分类列表
                this.getFieldList();
            },
            methods: {
                goNext(id) {
                    if (id === 'field') {
                        // 如果选择了按专业分类答题，显示分类选择器
                        this.showFieldSelector = true;
                        return;
                    }
                    
                    sessionStorage.type = id;
                    sessionStorage.field = ''; // 清除之前可能存在的专业分类选择
                    
                    if (parseInt(sessionStorage.shuf_option)){
                        window.location.href = "./timu_s.html"; 
                        return;
                    }
                    window.location.href = "./timu.html";
                },
                onSelect1(i) {
                    if (this.itemList[i].select) {
                        this.itemList[i].select = 0
                    } else {
                        this.itemList[i].select = 1
                    }
                    sessionStorage.shuf_option = this.itemList[i].select;
                },
                // 获取专业分类列表
                getFieldList() {
                    const fileId = sessionStorage.id;
                    const fileName = sessionStorage.file;
                    
                    // 从JSON文件中获取专业分类列表
                    axios.get('./json/' + fileName)
                        .then(response => {
                            const data = response.data;
                            // 提取所有不同的专业分类
                            const fields = new Set();
                            data.forEach(item => {
                                if (item.field && item.field.trim() !== '') {
                                    fields.add(item.field);
                                }
                            });
                            this.fieldList = Array.from(fields);
                        })
                        .catch(error => {
                            console.error('获取专业分类失败:', error);
                            this.$Message.error('获取专业分类失败');
                        });
                },
                // 选择专业分类
                selectField(field) {
                    this.selectedField = field;
                    
                    // 显示模式选择
                    this.$Modal.confirm({
                        title: '选择答题模式',
                        content: `您已选择"${field}"分类，请选择答题模式：`,
                        okText: '确定',
                        cancelText: '取消',
                        render: (h) => {
                            return h('div', [
                                h('RadioGroup', {
                                    props: {
                                        value: this.selectedMode
                                    },
                                    on: {
                                        'input': (value) => {
                                            this.selectedMode = value;
                                        }
                                    }
                                }, [
                                    h('Radio', {
                                        props: {
                                            label: 'order'
                                        }
                                    }, '顺序答题'),
                                    h('Radio', {
                                        props: {
                                            label: 'random'
                                        }
                                    }, '乱序答题'),
                                    h('Radio', {
                                        props: {
                                            label: 'recite'
                                        }
                                    }, '背题模式')
                                ])
                            ]);
                        },
                        onOk: () => {
                            if (!this.selectedMode) {
                                this.$Message.warning('请选择答题模式');
                                return;
                            }
                            
                            // 设置专业分类和答题模式
                            sessionStorage.field = field;
                            sessionStorage.type = this.selectedMode;
                            
                            // 跳转到相应页面
                            if (parseInt(sessionStorage.shuf_option)){
                                window.location.href = "./timu_s.html"; 
                            } else {
                                window.location.href = "./timu.html";
                            }
                        }
                    });
                }
            }
        })
    </script>
    </body>

</html>
