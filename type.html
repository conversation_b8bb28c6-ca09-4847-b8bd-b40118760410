<!--
 * @Author: mokevip
 * @Date: 2020-09-14 09:24:16
 * @LastEditors: mokevip
 * @LastEditTime: 2020-09-14 11:00:40
 * @Description: 
-->
<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Expires" content="0">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
        <meta http-equiv="Cache" content="no-cache">
        <title>答题类型选择</title>
        <link rel="stylesheet" href="./css/iview.css">
    <link rel="stylesheet" href="./css/modern-theme.css">
        <script>
        
    </script>
        <style>
        body {
            background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
            min-height: 100vh;
        }

        .mode-selector {
            max-width: 800px;
            margin: var(--space-xl) auto var(--space-lg) auto;
            padding: 0 var(--space-md);
        }

        .mode-title {
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: var(--space-md);
            text-align: center;
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: var(--space-sm);
            margin-bottom: var(--space-lg);
        }

        .item {
            background: var(--light-gray);
            color: var(--text-secondary);
            border-radius: var(--radius-small);
            padding: var(--space-xs) var(--space-sm);
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .item:hover {
            background: var(--hover-bg);
            border-color: var(--accent-blue);
        }

        .item1 {
            background: var(--accent-blue);
            color: var(--primary-white);
            border-color: var(--accent-blue);
        }

        .item1:hover {
            background: #357abd;
            border-color: #357abd;
        }

        .typeList {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 var(--space-md);
        }

        .typeList > div {
            margin-bottom: var(--space-md);
        }

        .timu {
            background: var(--primary-white);
            border: 1px solid var(--border-gray);
            border-radius: var(--radius-large);
            padding: var(--space-xl);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .timu:hover {
            background: var(--hover-bg);
            border-color: var(--accent-blue);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* 专业分类选择区域 */
        .field-selector {
            max-width: 1200px;
            margin: var(--space-xl) auto;
            padding: 0 var(--space-md);
        }

        .field-selector h3 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.5rem;
            margin-bottom: var(--space-md);
            text-align: center;
        }

        .field-info {
            background: var(--soft-white);
            border: 1px solid var(--border-gray);
            border-radius: var(--radius-medium);
            padding: var(--space-md);
            margin-bottom: var(--space-lg);
            text-align: center;
        }

        .field-info p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.9rem;
        }

        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: var(--space-md);
            margin: var(--space-lg) 0;
        }

        .field-card {
            background: var(--primary-white);
            border: 1px solid var(--border-gray);
            border-radius: var(--radius-medium);
            padding: var(--space-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: var(--shadow-light);
        }

        .field-card:hover {
            border-color: var(--accent-blue);
            background: var(--hover-bg);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .field-selected {
            border-color: var(--accent-blue);
            background: rgba(74, 144, 226, 0.05);
            box-shadow: var(--shadow-medium);
        }

        .field-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-sm);
            font-size: 1rem;
            line-height: 1.4;
        }

        .field-count {
            color: var(--text-secondary);
            font-size: 0.85rem;
            background: var(--light-gray);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-small);
            display: inline-block;
        }

        .field-actions {
            margin-top: var(--space-lg);
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .mode-selector {
                margin: var(--space-lg) auto;
            }

            .filter-tags {
                justify-content: flex-start;
            }

            .field-grid {
                grid-template-columns: 1fr;
                gap: var(--space-sm);
            }

            .field-selector {
                padding: 0 var(--space-sm);
            }
        }
    </style>

    </head>

    <body>
        <div id="el">
            <div class="mode-selector">
                <div class="mode-title">请选择答题模式：</div>
                <div class="filter-tags">
                    <label class='item' v-for="(item,i) in itemList" :key="i" :class="{'item1':item.select?true:false}" @click="onSelect1(i)">
                        {{item.text}}
                    </label>
                </div>
            </div>

            <div class="typeList">
                <div v-for="i in typeList" @click='goNext(i.id)'>
                    <card class="timu">
                        {{i.name}}
                    </card>
                </div>
            </div>
            
            <!-- 添加专业分类选择区域 -->
            <div v-if="showFieldSelector" class="field-selector">
                <h3>请选择专业分类</h3>
                <div class="field-info">
                    <p>共找到 {{fieldList.length}} 个专业分类，点击选择后可进行该分类下的专项练习</p>
                </div>

                <div class="field-grid">
                    <div v-for="(fieldInfo, index) in fieldListWithCount" :key="index"
                         class="field-card"
                         :class="{'field-selected': selectedField === fieldInfo.name}"
                         @click="selectField(fieldInfo.name)">
                        <div class="field-name">{{fieldInfo.name}}</div>
                        <div class="field-count">{{fieldInfo.count}}题</div>
                    </div>
                </div>

                <div class="field-actions">
                    <i-button @click="showFieldSelector = false" class="modern-btn">返回模式选择</i-button>
                    <i-button @click="goToFieldManager" class="modern-btn primary" style="margin-left: 10px;">
                        📚 专业分类管理
                    </i-button>
                </div>
            </div>
        </div>
        <script src="./js/vue.min.js"></script>
        <script src="./js/iview.min.js"></script>
        <script>
        const vue = new Vue({
            el: "#el",
            data: {
                itemList : [{
                    id: '1',
                    text: '打乱选项',
                    select: parseInt(sessionStorage.shuf_option)
                }],
                typeList: [{
                    id: 'random',
                    name: "乱序答题"
                }, {
                    id: 'order',
                    name: "顺序答题"
                }, {
                    id: 'recite',
                    name: "背题模式"
                }, {
                    id: 'wrong',
                    name: "错题模式"
                }, {
                    id: 'field',
                    name: "按专业分类答题"
                }],
                showFieldSelector: false,
                fieldList: [],
                fieldListWithCount: [], // 带题目数量的分类列表
                selectedField: '',
                selectedMode: '', // 存储用户选择的模式（顺序、乱序、背题）
                allQuestions: [] // 存储所有题目数据
            },
            mounted() {
                // 页面加载时获取专业分类列表
                this.getFieldList();
            },
            methods: {
                goNext(id) {
                    if (id === 'field') {
                        // 如果选择了按专业分类答题，显示分类选择器
                        this.showFieldSelector = true;
                        return;
                    }
                    
                    sessionStorage.type = id;
                    sessionStorage.field = ''; // 清除之前可能存在的专业分类选择
                    
                    if (parseInt(sessionStorage.shuf_option)){
                        window.location.href = "./timu_s.html"; 
                        return;
                    }
                    window.location.href = "./timu.html";
                },
                onSelect1(i) {
                    if (this.itemList[i].select) {
                        this.itemList[i].select = 0
                    } else {
                        this.itemList[i].select = 1
                    }
                    sessionStorage.shuf_option = this.itemList[i].select;
                },
                // 获取专业分类列表
                getFieldList() {
                    const fileId = sessionStorage.id;
                    const fileName = sessionStorage.file;

                    // 从JSON文件中获取专业分类列表
                    axios.get('./json/' + fileName)
                        .then(response => {
                            const data = response.data;
                            this.allQuestions = data; // 保存所有题目数据

                            // 统计每个分类的题目数量
                            const fieldCount = {};
                            data.forEach(item => {
                                if (item.一级纲要 && item.一级纲要.trim() !== '') {
                                    const field = item.一级纲要.trim();
                                    fieldCount[field] = (fieldCount[field] || 0) + 1;
                                }
                            });

                            // 创建带数量的分类列表
                            this.fieldListWithCount = Object.entries(fieldCount)
                                .map(([name, count]) => ({ name, count }))
                                .sort((a, b) => b.count - a.count); // 按题目数量降序排列

                            this.fieldList = this.fieldListWithCount.map(item => item.name);
                        })
                        .catch(error => {
                            console.error('获取专业分类失败:', error);
                            this.$Message.error('获取专业分类失败');
                        });
                },
                // 选择专业分类
                selectField(field) {
                    this.selectedField = field;

                    // 获取该分类的题目数量
                    const fieldInfo = this.fieldListWithCount.find(item => item.name === field);
                    const questionCount = fieldInfo ? fieldInfo.count : 0;

                    // 显示模式选择
                    this.$Modal.confirm({
                        title: '选择答题模式',
                        content: `您已选择"${field}"分类（共${questionCount}题），请选择答题模式：`,
                        okText: '开始答题',
                        cancelText: '取消',
                        width: 500,
                        render: (h) => {
                            return h('div', { style: 'padding: 20px 0;' }, [
                                h('div', { style: 'margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;' }, [
                                    h('h4', { style: 'margin: 0 0 10px 0; color: #333;' }, '分类信息'),
                                    h('p', { style: 'margin: 5px 0; color: #666;' }, `📚 分类名称：${field}`),
                                    h('p', { style: 'margin: 5px 0; color: #666;' }, `📊 题目数量：${questionCount}题`),
                                    h('p', { style: 'margin: 5px 0; color: #666;' }, `🎯 专项练习：针对该分类进行集中训练`)
                                ]),
                                h('h4', { style: 'margin: 20px 0 15px 0; color: #333;' }, '选择答题模式：'),
                                h('RadioGroup', {
                                    props: {
                                        value: this.selectedMode
                                    },
                                    on: {
                                        'input': (value) => {
                                            this.selectedMode = value;
                                        }
                                    },
                                    style: 'display: flex; flex-direction: column; gap: 12px;'
                                }, [
                                    h('Radio', {
                                        props: { label: 'order' },
                                        style: 'padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; margin: 0;'
                                    }, [
                                        h('div', [
                                            h('strong', '📖 顺序答题'),
                                            h('div', { style: 'color: #666; font-size: 12px; margin-top: 4px;' }, '按题目顺序依次练习，适合系统学习')
                                        ])
                                    ]),
                                    h('Radio', {
                                        props: { label: 'random' },
                                        style: 'padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; margin: 0;'
                                    }, [
                                        h('div', [
                                            h('strong', '🔀 乱序答题'),
                                            h('div', { style: 'color: #666; font-size: 12px; margin-top: 4px;' }, '随机打乱题目顺序，增加练习难度')
                                        ])
                                    ]),
                                    h('Radio', {
                                        props: { label: 'recite' },
                                        style: 'padding: 10px; border: 1px solid #e9ecef; border-radius: 6px; margin: 0;'
                                    }, [
                                        h('div', [
                                            h('strong', '📝 背题模式'),
                                            h('div', { style: 'color: #666; font-size: 12px; margin-top: 4px;' }, '直接显示答案和解析，适合快速记忆')
                                        ])
                                    ])
                                ])
                            ]);
                        },
                        onOk: () => {
                            if (!this.selectedMode) {
                                this.$Message.warning('请选择答题模式');
                                return false; // 阻止关闭对话框
                            }

                            // 设置专业分类和答题模式
                            sessionStorage.field = field;
                            sessionStorage.type = this.selectedMode;

                            // 显示开始提示
                            this.$Message.success(`开始${field}分类${this.getModeText(this.selectedMode)}，共${questionCount}题`);

                            // 延迟跳转，让用户看到提示
                            setTimeout(() => {
                                if (parseInt(sessionStorage.shuf_option)){
                                    window.location.href = "./timu_s.html";
                                } else {
                                    window.location.href = "./timu.html";
                                }
                            }, 1000);
                        }
                    });
                },

                // 获取模式文本
                getModeText(mode) {
                    const modeMap = {
                        'order': '顺序答题',
                        'random': '乱序答题',
                        'recite': '背题模式'
                    };
                    return modeMap[mode] || '答题';
                },

                // 跳转到专业分类管理页面
                goToFieldManager() {
                    window.location.href = "./field_manager.html";
                }
            }
        })
    </script>
    </body>

</html>
