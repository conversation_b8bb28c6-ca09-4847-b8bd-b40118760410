/* 现代化纯白主题配色方案 */

/* ===== 全局变量定义 ===== */
:root {
    /* 主色调 - 纯白系 */
    --primary-white: #ffffff;
    --soft-white: #fafafa;
    --light-gray: #f5f5f5;
    --border-gray: #e8e8e8;
    
    /* 文字颜色 - 低对比度 */
    --text-primary: #2c2c2c;
    --text-secondary: #666666;
    --text-muted: #999999;
    --text-light: #cccccc;
    
    /* 功能色彩 - 极简设计 */
    --accent-blue: #4a90e2;
    --success-green: #7ed321;
    --warning-orange: #f5a623;
    --error-red: #d0021b;
    
    /* 交互状态 */
    --hover-bg: #f8f9fa;
    --active-bg: #e9ecef;
    --focus-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    
    /* 阴影效果 - 极其轻微 */
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.08);
    
    /* 边框圆角 */
    --radius-small: 4px;
    --radius-medium: 8px;
    --radius-large: 12px;
    
    /* 间距系统 */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
}

/* ===== 全局重置 ===== */
* {
    box-sizing: border-box;
}

body {
    background-color: var(--primary-white);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 通用组件样式 ===== */

/* 卡片组件 */
.modern-card {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-light);
    transition: all 0.2s ease;
}

.modern-card:hover {
    box-shadow: var(--shadow-medium);
    border-color: var(--accent-blue);
}

/* 按钮组件 */
.modern-btn {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: var(--radius-small);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    padding: var(--space-sm) var(--space-md);
    transition: all 0.2s ease;
    outline: none;
}

.modern-btn:hover {
    background: var(--hover-bg);
    border-color: var(--accent-blue);
}

.modern-btn:focus {
    box-shadow: var(--focus-shadow);
}

.modern-btn.primary {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
    color: var(--primary-white);
}

.modern-btn.primary:hover {
    background: #357abd;
    border-color: #357abd;
}

.modern-btn.success {
    background: var(--success-green);
    border-color: var(--success-green);
    color: var(--primary-white);
}

.modern-btn.warning {
    background: var(--warning-orange);
    border-color: var(--warning-orange);
    color: var(--primary-white);
}

/* 输入框组件 */
.modern-input {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: var(--radius-small);
    color: var(--text-primary);
    font-size: 14px;
    padding: var(--space-sm) var(--space-md);
    transition: all 0.2s ease;
    outline: none;
    width: 100%;
}

.modern-input:focus {
    border-color: var(--accent-blue);
    box-shadow: var(--focus-shadow);
}

.modern-input::placeholder {
    color: var(--text-muted);
}

/* 标签组件 */
.modern-tag {
    background: var(--light-gray);
    border-radius: var(--radius-small);
    color: var(--text-secondary);
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
    padding: var(--space-xs) var(--space-sm);
}

.modern-tag.primary {
    background: rgba(74, 144, 226, 0.1);
    color: var(--accent-blue);
}

.modern-tag.success {
    background: rgba(126, 211, 33, 0.1);
    color: var(--success-green);
}

.modern-tag.warning {
    background: rgba(245, 166, 35, 0.1);
    color: var(--warning-orange);
}

/* 进度条 */
.modern-progress {
    background: var(--light-gray);
    border-radius: var(--radius-small);
    height: 4px;
    overflow: hidden;
}

.modern-progress-bar {
    background: var(--accent-blue);
    height: 100%;
    transition: width 0.3s ease;
}

/* 分割线 */
.modern-divider {
    border: none;
    border-top: 1px solid var(--border-gray);
    margin: var(--space-lg) 0;
}

/* 文本样式 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-light { color: var(--text-light); }

/* 背景样式 */
.bg-white { background-color: var(--primary-white); }
.bg-soft { background-color: var(--soft-white); }
.bg-light { background-color: var(--light-gray); }

/* 间距工具类 */
.m-0 { margin: 0; }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --space-md: 12px;
        --space-lg: 18px;
        --space-xl: 24px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--text-light);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
