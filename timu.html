<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
    <meta http-equiv="Cache" content="no-cache">
    <title>刷题</title>
    <link rel="stylesheet" href="./css/iview.css">
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();

    </script>
    <style>
        body {
            line-height: 1.4rem;
        }

        .process {
            width: 93.6vw;
            display: block;
            margin: 2vw auto 1vw;
            font-size: 0.8rem;
            letter-spacing: 0.1rem;
        }

        .timu {
            width: 96vw;
            margin: 2vw auto;
        }

        .option {
            margin: 0 0 2vw;
            line-height: 2rem;
            letter-spacing: 0.1rem;
        }

        .option div {
            padding: 0 2vw;
            border-radius: 0.6vw;
        }

        .option .active {
            background-color: blanchedalmond;
        }

        .type {
            font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #1890ff;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
        }

        .huida {
            width: 96vw;
            display: block;
            margin: 4vw auto;
        }

        .result {
            margin: 2vw 0 3vw;
            position: relative;
            padding: 8px 48px 8px 16px;
            border-radius: 4px;
            color: #515a6e;
            font-size: 12px;
            line-height: 16px;
            margin-bottom: 10px;
        }

        .result.success {
            border: 1px solid #8ce6b0;
            background-color: #edfff3;
        }

        .result.warning {
            border: 1px solid #ffd77a;
            background-color: #fff9e6;
        }

        .result.info {
            border: 1px solid #abdcff;
            background-color: #f0faff;
        }

        .answer {
            white-space: pre-line;
        }

        .analysis {
            margin-top: 1vw;
        }

        .pageControl {
            text-align: center;
            display: block;
            margin-top: 20px;
        }

        .ivu-card-head p {
            overflow: inherit;
            white-space: inherit;
            line-height: 24px;
        }

        .ivu-message-notice-content-text {
            display: block !important;
        }
        
        /* 新增样式 */
        .field {
            font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #19be6b; /* 绿色背景 */
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
        }
        
        .category {
            font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #ff9900; /* 橙色背景 */
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
        }
        
        /* 笔记区域样式 */
        .notes-section {
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
    </style>
</head>

<body>
    <div id="el" v-clickoutside="handleClose">
        <div class='process'>
            第{{page+1}}题，共{{data.length}}题
            <span v-if="sessionStorage.field" style="margin-left: 10px; font-weight: bold; color: #ff9900;">
                [{{sessionStorage.field}}]
            </span>
        </div>
        <ButtonGroup class="pageControl">
            <i-button type="primary" @click="prev">上一题</i-button>
            <i-button type="primary" @click="next">下一题</i-button>
            <i-button type="info" @click="importNotes">导入笔记</i-button>
            <input type="file" ref="fileInput" style="display: none" @change="handleFileUpload" accept=".json">
            <input type='hidden' v-on:keydown.space.native="next"/>
            <input type='hidden' v-on:keydown.left.native="prev"/>
            <input type='hidden' v-on:keydown.right.native="next"/>
            <input type='hidden' v-on:keydown.up.native="prev"/>
            <input type='hidden' v-on:keydown.down.native="next"/>
            <input type='hidden' v-clickoutside="handleClose"/>
        </ButtonGroup>
            
        <Card class='timu'>
            <p slot="title">
                <!-- <label class='type' v-if="timu.option">{{timu.option.length?(timu.answer.length===1?'单选':'多选'):(timu.answer.length>16?'简答':'填空')}}</label> -->
                <!-- 显示题目类型和专业分类 -->
                <label class='field' v-if="timu.type">{{timu.type}}</label>
                <label class='category' v-if="timu.field">{{timu.field}}</label>
                {{timu.title}}
            </p>
            <!-- <a href="#" slot="extra" @click.prevent="changeLimit">
                <Icon type="ios-loop-strong"></Icon>
                Change
            </a> -->
            <div class="option">
                <div v-for="(it,id) in timu.option" v-key='id' v-if="it!=''" @click='select(id)' :class='daan.indexOf(id)!=-1?"active":""'>
                    {{zidian[id]}}.{{it}}</div>
            </div>
            <div type="success" v-if='answer&&result===1' class='result success'>正确</div>
            <div type="warning" v-if='answer&&result===0' class='result warning'>抱歉回答错误</div>
            <div v-if='answer&&result===2' class='result info'>{{(String(timu.answer).length>16?'简答':'填空')}}题暂不支持回答</div>
        </Card>
        <i-button class="huida" type="primary" :disabled='daan.length===0' @click='huida' size="large" long>确定答案
        </i-button>
        <i-button class="huida" type="success" v-if='sessionStorage.type == "wrong"' @click='del' size="large" long>
            我已学会，不再显示。
        </i-button>
        </br>
        </br>
        </br>
        </br>
        </br></br>
        <Card class='timu'>
            <div v-if='answer' class="answer">答案：{{timu.answer}}</div>
            <!-- <div v-if='answer' class="answer">答案选项：{{timu.right_answer_content_arr}}</div> -->
            <div v-for="(it,id) in timu.option" v-key='id' v-if="timu.right_answer_content_arr.includes(it)" >
               答案选项：{{zidian[id]}}.{{it}}</div>
            <div v-if='answer' class="analysis">解析：{{timu.analysis?timu.analysis:'该答案暂无解析'}}</div>
            
            <!-- 添加解题思路记录区域 -->
            <div class="notes-section">
                <h3>我的解题思路</h3>
                <i-input type="textarea" v-model="noteContent" :rows="4" placeholder="记录您的解题思路和心得..."></i-input>
                <i-button type="primary" @click="saveNote" style="margin-top: 10px;">保存笔记</i-button>
                <i-button type="success" @click="exportNotes" style="margin-top: 10px; margin-left: 10px;">导出所有笔记</i-button>
            </div>
        </Card>
    </div>
    <script src="./js/public.js?version=1.0"></script>
    <script src="./js/vue.min.js"></script>
    <script src="./js/iview.min.js"></script>
    <script src="./js/axios.min.js"></script>
    <script>
       const clickoutside = {
    // 初始化指令
    bind(el, binding, vnode) {
        function documentHandler(e) {
            // 这里判断点击的元素是否是本身，是本身，则返回
            if (el.contains(e.target)) {
                return false;
            }
            // 判断指令中是否绑定了函数
            if (binding.expression) {
                // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
                binding.value(e);
            }
        }
        // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
        el.__vueClickOutside__ = documentHandler;
        document.addEventListener('click', documentHandler);
        },
    update() {},
    unbind(el, binding) {
        // 解除事件监听
        document.removeEventListener('click', el.__vueClickOutside__);
        delete el.__vueClickOutside__;
        },
    }
    </script>

    <script>
        const vue = new Vue({
            el: "#el",
            data: {
                fileName: '',
                fileId: '',
                data: [],
                page: 0,
                timu: {},
                zidian: ['A', 'B', 'C', 'D', 'E', 'F'],
                daan: [],
                answer: false,
                result: false,
                noteContent: '' // 添加笔记内容字段
            },
            mounted() {
                const that = this;
                document.addEventListener('keydown', that.handleWatchEnter);
            },
            directives: { clickoutside },
            methods: {
                handleWatchEnter(e) {//绑定键盘左右键
                var key = window.event ? e.keyCode : e.which;
                if (key === 37) {
                // 这里执行相应的行为动作
                      this.prev();
                    };
                if (key === 39) {
                // 这里执行相应的行为动作
                      this.next();
                    };
                if (key === 32) {
                // 这里执行相应的行为动作
                      this.next();
                    }
                },
                handleClose(e) {
                    this.next();
                },

                select(k) {
                    if (this.daan.indexOf(k) != -1) {
                        this.daan.splice(this.daan.findIndex(item => item === k), 1);
                    } else {
                        //单选同时只能选一项
                        if (String(this.timu.answer).length === 1) {
                            this.daan = [];
                        }
                        this.daan.push(k)
                    }
                },
                huida() {
                    let answer = this.timu.answer.split('')
                    this.result = 1;
                    if (this.daan.length != answer.length) {
                        this.result = 0;
                    } else {
                        for (i in this.daan) {
                            //console.log(this.zidian[this.daan[i]])
                            if (answer.indexOf(this.zidian[this.daan[i]]) == -1) {
                                this.result = 0;
                            }
                        }
                    }
                    if (this.result) {
                        setTimeout(() => {
                            this.next()
                        }, 800)
                    } else {
                        //错题ID记录
                        if (!localStorage["wrong_" + this.fileId]) {
                            localStorage["wrong_" + this.fileId] = JSON.stringify([])
                        }
                        let wrong = JSON.parse(localStorage["wrong_" + this.fileId])
                        if (wrong.indexOf(this.timu.id) == -1) {
                            wrong.push(this.timu.id)
                            localStorage["wrong_" + this.fileId] = JSON.stringify(wrong)
                        }
                    }
                    this.answer = true;
                },
                // 恢复答题进度
                recovery(type) {
                    if (localStorage[type + '_' + this.fileId] && Number(localStorage[type + '_' + this.fileId])) {
                        const num = Number(localStorage[type + '_' + this.fileId])
                        this.$Modal.confirm({
                            title: '恢复刷题进度',
                            content: '检测到你上次答到第' + (num + 1) + '题，是否继续？',
                            onOk: () => {
                                this.page = num
                                this.initTimu()
                            }
                        })
                    }
                },
                //删除错题
                del() {
                    let wrong = JSON.parse(localStorage["wrong_" + this.fileId])
                    let index = wrong.indexOf(this.timu.id)
                    //console.log(index)
                    wrong.splice(index, 1)
                    localStorage["wrong_" + this.fileId] = JSON.stringify(wrong)
                    this.$Message.success('删除错题记录成功！');
                    window.location.reload();
                },
                next() {
                    if (this.page < this.data.length) {
                        this.page += 1;
                        this.initTimu()
                    }
                },
                prev() {
                    if (this.page !== 0) {
                        this.page -= 1;
                        this.initTimu()
                    }
                },
                prev2() {
                    if (this.page !== 0) {
                        this.page -= 2;
                        this.initTimu()
                    }
                },
                //打乱数组顺序
                randomArray(array) {
                    var m = array.length,
                        t, i;
                    while (m) {
                        i = Math.floor(Math.random() * m--);
                        t = array[m];
                        array[m] = array[i];
                        array[i] = t;
                    }
                    return array;
                },
                removeEmpty(arr) {
                    return arr.filter(function(item) {
                    return item !== null && item !== undefined && item !== '';
                     });
                },
                //打乱数组顺序2 - vu
                randomArray2(arr) {
                    var m = arr.length;
                    for (let i = arr.length-1; i>=0; i--){
                        let j = Math.floor(Math.random() * i );
                        var t = arr[i];
                        arr[i] = arr[j];
                        arr[j] = t;
                    }
                    return arr;//指打乱顺序，不去空选项
                    // return this.removeEmpty(arr)
                },
                right_answer_content_arr(arr) {//选项乱序前 保存答案选项内容的列表
                    let right_answer_zidian = arr.answer.split('')
                    //console.log(arr,1000,right_answer_zidian)
                    let res = [];
                    if (right_answer_zidian.length == 0) {
                        return res;
                    } else {
                        for (it in right_answer_zidian) {//console.log(1111,right_answer_zidian[it],this.zidian.indexOf(right_answer_zidian[it]),arr.option[this.zidian.indexOf(right_answer_zidian[it])])
                            res.push(arr.option[this.zidian.indexOf(right_answer_zidian[it])]);
                            }
                        }
                    return res;
                 },
                right_answer_aftr_shuf(shuf_arr,right_answer_content_arr) {//选项乱序后 保存答案选项到this.timu.answer
                    // let right_answer_zidian = shuf_arr.answer.split('')
                    //console.log(shuf_arr,1000,right_answer_zidian)
                    let right_answer = [];
                    if (right_answer_content_arr.length == 0) {
                        return right_answer;
                    } else {
                        for (let it in shuf_arr.option) {
                            for (let jt in right_answer_content_arr) {
                                if (shuf_arr.option[it] === right_answer_content_arr[jt]) {
                                    right_answer.push(this.zidian[it]);
                                }
                            }
                        }
                        }
                    return right_answer.join('');
                 },
                initTimu() { //题目控制
                    //<!-- console.log(this.timu.option.sort(randomsort)) -->
                    //console.log(this.randomArray(this.timu.option))
                    //对之前的记录清空
                    this.daan = [];
                    this.answer = false;
                    //新题目
                    this.timu = this.data[this.page];
                    this.timu.right_answer_content_arr = this.right_answer_content_arr(this.timu);
                    //console.log('right_answer_content_arr',this.timu.right_answer_content_arr)
                    // this.randomArray2(this.timu.option);//指打乱顺序，不去空选项
                    // this.randomArray2(this.removeEmpty(this.timu.option));//指打乱顺序，去空选项
                    //console.log('this.timu.option shuffled',this.randomArray2(this.timu.option))
                    this.timu.answer = this.right_answer_aftr_shuf(this.timu,  this.timu.right_answer_content_arr)
                    // console.log('this.timu.answer no shuffled',this.timu.answer)
                    //简答题直接显示解析和答案
                    if (this.timu.option.length == 0) {
                        this.answer = true;
                        this.result = 2;
                    }
                    //背题模式直接显示解析和答案
                    if (sessionStorage.type == "recite") {
                        this.answer = true;
                        this.result = 3; //不显示结果框
                    }
                    // 记录答题题号
                    if (sessionStorage.type === 'order') {
                        localStorage['order_' + this.fileId] = this.page
                    }
                    if (sessionStorage.type === 'recite') {
                        localStorage['recite_' + this.fileId] = this.page
                    }
                },

                //将现有数组中的错题提取出来
                wrongArray(array) {
                    let wrong = JSON.parse(localStorage["wrong_" + this.fileId])
                    let result = [];
                    
                    for (let i in wrong) {
                        for (let j in array) {
                            if (wrong[i] === array[j].id) {
                                // 如果设置了专业分类，只添加匹配的错题
                                if (sessionStorage.field && sessionStorage.field !== '') {
                                    if (array[j].field === sessionStorage.field) {
                                        result.push(array[j]);
                                    }
                                } else {
                                    result.push(array[j]);
                                }
                            }
                        }
                    }
                    
                    // 如果筛选后没有错题，显示提示
                    if (result.length === 0 && sessionStorage.field && sessionStorage.field !== '') {
                        this.$Message.warning(`"${sessionStorage.field}"分类下没有错题记录`);
                    }
                    
                    //错题乱序返回
                    return this.randomArray(result);
                },
                // 加载笔记
                loadNote() {
                    if (!this.timu || !this.timu.id) return;
                    
                    // 从localStorage加载笔记
                    const noteKey = `note_${this.fileId}_${this.timu.id}`;
                    const savedNote = localStorage.getItem(noteKey);
                    this.noteContent = savedNote || '';
                },
                
                // 保存笔记
                saveNote() {
                    if (!this.timu || !this.timu.id) return;
                    
                    // 保存到localStorage
                    const noteKey = `note_${this.fileId}_${this.timu.id}`;
                    localStorage.setItem(noteKey, this.noteContent);
                    
                    // 更新笔记索引
                    let noteIndex = JSON.parse(localStorage.getItem(`noteIndex_${this.fileId}`) || '[]');
                    if (!noteIndex.includes(this.timu.id)) {
                        noteIndex.push(this.timu.id);
                        localStorage.setItem(`noteIndex_${this.fileId}`, JSON.stringify(noteIndex));
                    }
                    
                    this.$Message.success('笔记保存成功！');
                },
                
                // 导出所有笔记
                exportNotes() {
                    // 获取当前题库的所有笔记
                    let noteIndex = JSON.parse(localStorage.getItem(`noteIndex_${this.fileId}`) || '[]');
                    let allNotes = {};
                    
                    // 收集所有笔记
                    noteIndex.forEach(questionId => {
                        const noteKey = `note_${this.fileId}_${questionId}`;
                        const noteContent = localStorage.getItem(noteKey);
                        if (noteContent) {
                            // 查找题目信息
                            const question = this.data.find(q => q.id === questionId);
                            if (question) {
                                allNotes[questionId] = {
                                    title: question.title,
                                    type: question.type || '未分类',
                                    field: question.field || '未分类',
                                    content: noteContent
                                };
                            } else {
                                // 如果在当前筛选的数据中找不到题目，尝试从原始数据中查找
                                axios.get('./json/' + this.fileName)
                                    .then(response => {
                                        const allData = response.data;
                                        const fullQuestion = allData.find(q => q.id === questionId);
                                        if (fullQuestion) {
                                            allNotes[questionId] = {
                                                title: fullQuestion.title,
                                                type: fullQuestion.type || '未分类',
                                                field: fullQuestion.field || '未分类',
                                                content: noteContent
                                            };
                                        } else {
                                            allNotes[questionId] = {
                                                title: '未知题目',
                                                type: '未分类',
                                                field: '未分类',
                                                content: noteContent
                                            };
                                        }
                                    })
                                    .catch(error => {
                                        console.error('获取题目信息失败:', error);
                                    });
                            }
                        }
                    });
                    
                    // 创建下载文件
                    setTimeout(() => {
                        const notesJson = JSON.stringify(allNotes, null, 2);
                        const blob = new Blob([notesJson], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        
                        // 创建下载链接
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `笔记_${this.fileName.replace('.json', '')}_${sessionStorage.field || '全部'}_${new Date().toISOString().slice(0, 10)}.json`;
                        document.body.appendChild(a);
                        a.click();
                        
                        // 清理
                        setTimeout(() => {
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                        }, 0);
                        
                        this.$Message.success('笔记导出成功！');
                    }, 500); // 给异步操作一些时间完成
                },
                
                // 修改initTimu方法，加载笔记
                initTimu() {
                    // 保留原有代码
                    this.daan = [];
                    this.answer = false;
                    this.timu = this.data[this.page];
                    this.timu.right_answer_content_arr = this.right_answer_content_arr(this.timu);
                    // ... 其他原有代码 ...
                    
                    // 加载当前题目的笔记
                    this.$nextTick(() => {
                        this.loadNote();
                    });
                },
                // 触发文件选择
                importNotes() {
                    this.$refs.fileInput.click();
                },
                
                // 处理文件上传
                handleFileUpload(event) {
                    const file = event.target.files[0];
                    if (!file) return;
                    
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const notes = JSON.parse(e.target.result);
                            let importCount = 0;
                            
                            // 导入笔记
                            for (const [questionId, noteData] of Object.entries(notes)) {
                                const noteKey = `note_${this.fileId}_${questionId}`;
                                localStorage.setItem(noteKey, noteData.content);
                                importCount++;
                                
                                // 更新笔记索引
                                let noteIndex = JSON.parse(localStorage.getItem(`noteIndex_${this.fileId}`) || '[]');
                                if (!noteIndex.includes(questionId)) {
                                    noteIndex.push(questionId);
                                    localStorage.setItem(`noteIndex_${this.fileId}`, JSON.stringify(noteIndex));
                                }
                            }
                            
                            this.$Message.success(`成功导入 ${importCount} 条笔记！`);
                            
                            // 刷新当前笔记
                            this.loadNote();
                        } catch (error) {
                            this.$Message.error('导入失败，文件格式不正确');
                            console.error('导入笔记失败:', error);
                        }
                    };
                    reader.readAsText(file);
                }
            },
            created() {
                this.fileName = sessionStorage.file
                this.fileId = sessionStorage.id
                if (!this.fileName) {
                    window.location.href = "./index.html"
                }
                axios.get('./json/' + this.fileName)
                    .then((response) => {
                        this.data = response.data;
                        
                        // 如果设置了专业分类，则按专业分类筛选题目
                        if (sessionStorage.field && sessionStorage.field !== '') {
                            const field = sessionStorage.field;
                            this.data = this.data.filter(item => item.field === field);
                            
                            // 如果筛选后没有题目，显示提示
                            if (this.data.length === 0) {
                                this.$Message.error(`没有找到"${field}"分类的题目`);
                                // 返回选择页面
                                setTimeout(() => {
                                    window.location.href = "./type.html";
                                }, 2000);
                                return;
                            }
                        }
                        
                        //错题模式需要筛选题目
                        if (sessionStorage.type == "wrong") {
                            //如果没有错题
                            if (localStorage["wrong_" + this.fileId] && localStorage["wrong_" + this.fileId] != "[]") {
                                this.data = this.wrongArray(this.data)
                            } else { //如果没有错题
                                this.$Message.error('您暂时无错题记录，已自动为您选择乱序答题模式');
                                sessionStorage.type = "random";
                            }
                        }
                        //乱序模式需要随机排序题目
                        if (sessionStorage.type == "random") {
                            this.data = this.randomArray(this.data)
                        }

                        if (sessionStorage.type == "order" || sessionStorage.type == "recite") {
                            this.recovery(sessionStorage.type)
                        }

                        //初始化题目
                        this.initTimu();
                    })
                    .catch(function (error) {
                        this.$Message.error('发生错误！');
                    });
            }
        })
    </script>
</body>

</html>
