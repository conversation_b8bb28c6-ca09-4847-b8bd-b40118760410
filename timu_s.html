<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
    <meta http-equiv="Cache" content="no-cache">
    <title>刷题</title>
    <link rel="stylesheet" href="./css/iview.css">
    <style>
        body {
            line-height: 1.4rem;
        }

        .process {
            width: 93.6vw;
            display: block;
            margin: 2vw auto 1vw;
            font-size: 0.8rem;
            letter-spacing: 0.1rem;
        }

        .timu {
            width: 96vw;
            margin: 2vw auto;
        }

        .option {
            margin: 0 0 2vw;
            line-height: 2rem;
            letter-spacing: 0.1rem;
        }

        .option div {
            padding: 0 2vw;
            border-radius: 0.6vw;
        }

        .option .active {
            background-color: blanche<PERSON>mond;
        }

        .type {
            font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #1890ff;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
        }

        .huida {
            width: 96vw;
            display: block;
            margin: 4vw auto;
        }

        .result {
            margin: 2vw 0 3vw;
            position: relative;
            padding: 8px 48px 8px 16px;
            border-radius: 4px;
            color: #515a6e;
            font-size: 12px;
            line-height: 16px;
            margin-bottom: 10px;
        }

        .result.success {
            border: 1px solid #8ce6b0;
            background-color: #edfff3;
        }

        .result.warning {
            border: 1px solid #ffd77a;
            background-color: #fff9e6;
        }

        .result.info {
            border: 1px solid #abdcff;
            background-color: #f0faff;
        }

        .answer {
            white-space: pre-line;
        }

        .analysis {
            margin-top: 1vw;
        }

        .pageControl {
            text-align: center;
            display: block;
            margin-top: 20px;
        }

        .ivu-card-head p {
            overflow: inherit;
            white-space: inherit;
            line-height: 24px;
        }

        .ivu-message-notice-content-text {
            display: block !important;
        }
        
    </style>
</head>

<body>
    <div id="el"  v-clickoutside="handleClose">
        <div class='process'>
            第{{page+1}}题，共{{data.length}}题
            <span v-if="sessionStorage.field" style="margin-left: 10px; font-weight: bold; color: #ff9900;">
                [{{sessionStorage.field}}]
            </span>
        </div>
        <ButtonGroup class="pageControl">
            <!-- <i-button type="primary"  id='prevs' @click="prev2">上一题</i-button> -->
            <!-- <i-button type="primary" @click="">下一题</i-button> -->
            <i-button type="primary"  id='prevs' @click="prev">上一题</i-button>
            <i-button type="primary" @click="next">下一题</i-button>
            <i-button :type="autoNext ? 'success' : 'default'" @click="toggleAutoNext">
                {{autoNext ? '✓ 答对自动跳转' : '答对自动跳转'}}
            </i-button>
            <input type='hidden' v-on:keydown.space.native="next"/>
            <input type='hidden' v-on:keydown.left.native="prev"/>
            <input type='hidden' v-on:keydown.right.native="next"/>
            <input type='hidden' v-on:keydown.up.native="prev"/>
            <input type='hidden' v-on:keydown.down.native="next"/>
            <!-- <input type='primary' id='handleClose' v-clickoutside="handleClose"/> -->
        </ButtonGroup>

        <Card class='timu'>
            <p slot="title">
                <!-- <label class='type' v-if="timu.option">{{timu.option.length?(timu.answer.length===1?'单选':'多选'):(timu.answer.length>16?'简答':'填空')}}</label> -->
                <!-- 显示题目类型和专业分类 -->
                <label class='field' v-if="timu.题型">{{timu.题型}}</label>
                <label class='category' v-if="timu.一级纲要">{{timu.一级纲要}}</label>
                {{timu.题干}}
            </p>
            <!-- <a href="#" slot="extra" @click.prevent="changeLimit">
                <Icon type="ios-loop-strong"></Icon>
                Change
            </a> -->
            <div class="option">
                <div v-for="(it,id) in timu.选项" v-key='id' v-if="it!=''" @click='select(id)' :class='daan.indexOf(id)!=-1?"active":""'>
                    {{zidian[id]}}.{{it}}</div>
            </div>
            <div type="success" v-if='answer&&result===1' class='result success'>正确</div>
            <div type="warning" v-if='answer&&result===0' class='result warning'>抱歉回答错误</div>
            <div v-if='answer&&result===2' class='result info'>{{(String(timu.答案).length>16?'简答':'填空')}}题暂不支持回答</div>
        </Card>
        <i-button class="huida" type="primary" :disabled='daan.length===0' @click='huida' size="large" long>确定答案</i-button>
        <i-button class="huida" type="success" v-if='sessionStorage.type == "wrong"' @click='del' size="large" long>
            我已学会，不再显示。
        </i-button>
        </br>
        </br>
        </br>
        </br>
        </br>
        </br>
        <Card class='timu'>
            <div v-if='answer' class="answer">答案：{{timu.答案}}</div>
            <!-- <div v-if='answer' class="answer">答案选项：{{timu.right_answer_content_arr}}</div> -->
            <div v-for="(it,id) in timu.选项" v-key='id' v-if="timu.right_answer_content_arr.includes(it)" >
               答案选项：{{zidian[id]}}.{{it}}</div>
            <div v-if='answer' class="analysis">解析：{{timu.判断题解析?timu.判断题解析:'该答案暂无解析'}}</div>
        </Card>
    </div>
    <script src="./js/public.js?version=1.0"></script>
    <script src="./js/vue.min.js"></script>
    <script src="./js/iview.min.js"></script>
    <script src="./js/axios.min.js"></script>
    <script>
    const clickoutside = {
    // 初始化指令
    bind(el, binding, vnode) {
        function documentHandler(e) {
            // 这里判断点击的元素是否是本身，是本身，则返回
            //if (document.contains(e.target)){
            //    console.log("1-e.target",e.target,e.target.type,e.target.attributes.id)
            //    console.log("1-el",el,el.type,el.id)
            //    console.log("el===e.target",el==e.target, el===e.target, el.contains(e.target))
            //    return false;
            //}
            if (el != e.target) {
                //console.log("2-e.target",e.target,e.target.type,e.target.attributes.id)
                //console.log("2-el",el,el.type,el.id)
                return false;
            }
            // 判断指令中是否绑定了函数
            if (binding.expression) {
                // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
                binding.value(e);
            }
            //console.log("3-e.target",e.target,e.target.type,e.target.id)
            //console.log("3-el",el,el.type,el.id)
        }
        // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
        el.__vueClickOutside__ = documentHandler;
        document.addEventListener('click', documentHandler);
        },
    update() {},
    unbind(el, binding) {
        // 解除事件监听
        document.removeEventListener('click', el.__vueClickOutside__);
        delete el.__vueClickOutside__;
        },
    }

        const vue = new Vue({
            el: "#el",
            data: {
                fileName: '',
                fileId: '',
                data: [],
                page: 0,
                timu: {},
                zidian: ['A', 'B', 'C', 'D', 'E', 'F'],
                daan: [],
                answer: false,
                result: false,
                autoNext: true // 默认开启答对自动跳转功能
            },
            mounted() {
                const that = this;
                document.addEventListener('keydown', that.handleWatchEnter);

                // 从localStorage读取自动跳转设置
                const savedAutoNext = localStorage.getItem('autoNext');
                if (savedAutoNext !== null) {
                    this.autoNext = savedAutoNext === 'true';
                }
            },

            directives :  {clickoutside},
            methods: {
                handleWatchEnter(e) {//绑定键盘左右键
                var key = window.event ? e.keyCode : e.which;
                if (key === 37) {
                // 这里执行相应的行为动作
                      this.prev();
                    };
                if (key === 39) {
                // 这里执行相应的行为动作
                      this.next();
                    };
                if (key === 32) {
                // 这里执行相应的行为动作
                      this.next();
                    }
                },
                handleClose(e) {
                    // 移除自动跳转功能，避免点击任意地方都跳转
                    // this.next();
                },

                select(k) {
                    if (this.daan.indexOf(k) != -1) {
                        this.daan.splice(this.daan.findIndex(item => item === k), 1);
                    } else {
                        //单选同时只能选一项
                        if (String(this.timu.答案).length === 1) {
                            this.daan = [];
                        }
                        this.daan.push(k);

                        // 检查是否选中了正确答案，如果是则自动跳转下一题
                        this.checkAnswerAndAutoNext();
                    }
                },

                // 检查答案是否正确，如果正确则自动跳转下一题
                checkAnswerAndAutoNext() {
                    let answer = this.timu.答案.split('');
                    let isCorrect = true;

                    // 检查答案长度是否匹配
                    if (this.daan.length != answer.length) {
                        isCorrect = false;
                    } else {
                        // 检查每个选项是否正确
                        for (let i in this.daan) {
                            if (answer.indexOf(this.zidian[this.daan[i]]) == -1) {
                                isCorrect = false;
                                break;
                            }
                        }
                    }

                    // 如果答案正确且开启了自动跳转功能，延迟跳转到下一题
                    if (isCorrect && this.autoNext) {
                        // 显示正确提示
                        this.answer = true;
                        this.result = 1;

                        // 延迟800ms后自动跳转到下一题
                        setTimeout(() => {
                            this.next();
                        }, 800);
                    }
                },

                // 切换自动跳转功能
                toggleAutoNext() {
                    this.autoNext = !this.autoNext;
                    // 保存设置到localStorage
                    localStorage.setItem('autoNext', this.autoNext);
                    this.$Message.info(this.autoNext ? '已开启答对自动跳转' : '已关闭答对自动跳转');
                },
                huida() {
                    let answer = this.timu.答案.split('')
                    this.result = 1;
                    if (this.daan.length != answer.length) {
                        this.result = 0;
                    } else {
                        for (i in this.daan) {
                            //console.log(this.zidian[this.daan[i]])
                            if (answer.indexOf(this.zidian[this.daan[i]]) == -1) {
                                this.result = 0;
                            }
                        }
                    }
                    if (this.result) {
                        setTimeout(() => {
                            this.next()
                        }, 800)
                    } else {
                        //错题ID记录
                        if (!localStorage["wrong_" + this.fileId]) {
                            localStorage["wrong_" + this.fileId] = JSON.stringify([])
                        }
                        let wrong = JSON.parse(localStorage["wrong_" + this.fileId])
                        if (wrong.indexOf(this.timu.序号) == -1) {
                            wrong.push(this.timu.序号)
                            localStorage["wrong_" + this.fileId] = JSON.stringify(wrong)
                        }
                    }
                    this.answer = true;
                },
                // 恢复答题进度
                recovery(type) {
                    if (localStorage[type + '_' + this.fileId] && Number(localStorage[type + '_' + this.fileId])) {
                        const num = Number(localStorage[type + '_' + this.fileId])
                        this.$Modal.confirm({
                            title: '恢复刷题进度',
                            content: '检测到你上次答到第' + (num + 1) + '题，是否继续？',
                            onOk: () => {
                                this.page = num
                                this.initTimu()
                            }
                        })
                    }
                },
                //删除错题
                del() {
                    let wrong = JSON.parse(localStorage["wrong_" + this.fileId])
                    let index = wrong.indexOf(this.timu.序号)
                    //console.log(index)
                    wrong.splice(index, 1)
                    localStorage["wrong_" + this.fileId] = JSON.stringify(wrong)
                    this.$Message.success('删除错题记录成功！');
                    window.location.reload();
                },
                next() {
                    if (this.page < this.data.length) {
                        this.page += 1;
                        this.initTimu()
                    }
                },
                prev() {
                    if (this.page !== 0) {
                        this.page -= 1;
                        this.initTimu()
                    }
                },
                prev2() {
                    if (this.page !== 0) {
                        this.page -= 2;
                        this.initTimu()
                    }
                },
                //打乱数组顺序
                randomArray(array) {
                    var m = array.length,
                        t, i;
                    while (m) {
                        i = Math.floor(Math.random() * m--);
                        t = array[m];
                        array[m] = array[i];
                        array[i] = t;
                    }
                    return array;
                },
                removeEmpty(arr) {
                    return arr.filter(function(item) {
                    return item !== null && item !== undefined && item !== '';
                     });
                },
                //打乱数组顺序2 - vu
                randomArray2(arr) {
                    var m = arr.length;
                    for (let i = arr.length-1; i>=0; i--){
                        let j = Math.floor(Math.random() * i );
                        var t = arr[i];
                        arr[i] = arr[j];
                        arr[j] = t;
                    }
                    return arr;//指打乱顺序，不去空选项
                    // return this.removeEmpty(arr)
                },
                right_answer_content_arr(arr) {//选项乱序前 保存答案选项内容的列表
                    let right_answer_zidian = arr.答案.split('')
                    //console.log(arr,1000,right_answer_zidian)
                    let res = [];
                    if (right_answer_zidian.length == 0) {
                        return res;
                    } else {
                        for (it in right_answer_zidian) {//console.log(1111,right_answer_zidian[it],this.zidian.indexOf(right_answer_zidian[it]),arr.选项[this.zidian.indexOf(right_answer_zidian[it])])
                            res.push(arr.选项[this.zidian.indexOf(right_answer_zidian[it])]);
                            }
                        }
                    return res;
                 },
                right_answer_aftr_shuf(shuf_arr,right_answer_content_arr) {//选项乱序后 保存答案选项到this.timu.answer
                    let right_answer_zidian = shuf_arr.答案.split('')
                    //console.log(shuf_arr,1000,right_answer_zidian)
                    let right_answer = [];
                    if (right_answer_content_arr.length == 0) {
                        return right_answer;
                    } else {
                        for (let it in shuf_arr.选项) {
                            for (let jt in right_answer_content_arr) {
                                if (shuf_arr.选项[it] === right_answer_content_arr[jt]) {
                                    right_answer.push(this.zidian[it]);
                                }
                            }
                        }
                        }
                    return right_answer.join('');
                 },
                initTimu() { //题目控制
                    //<!-- console.log(this.timu.option.sort(randomsort)) -->
                    //console.log(this.randomArray(this.timu.option))
                    //对之前的记录清空
                    this.daan = [];
                    this.answer = false;
                    //新题目
                    this.timu = this.data[this.page];
                    this.timu.right_answer_content_arr = this.right_answer_content_arr(this.timu);
                    //console.log('right_answer_content_arr',this.timu.right_answer_content_arr)
                    // this.randomArray2(this.timu.选项);//指打乱顺序，不去空选项
                    this.timu.选项 = this.randomArray2(this.removeEmpty(this.timu.选项));//指打乱顺序，去空选项
                    //console.log('this.timu.选项 shuffled',this.randomArray2(this.timu.选项))
                    this.timu.答案 = this.right_answer_aftr_shuf(this.timu,this.timu.right_answer_content_arr)
                    //console.log('this.timu.答案 shuffled',this.randomArray2(this.timu.答案))
                    //简答题直接显示解析和答案
                    if (this.timu.选项.length == 0) {
                        this.answer = true;
                        this.result = 2;
                    }
                    //背题模式直接显示解析和答案
                    if (sessionStorage.type == "recite") {
                        this.answer = true;
                        this.result = 3; //不显示结果框
                    }
                    // 记录答题题号
                    if (sessionStorage.type === 'order') {
                        localStorage['order_' + this.fileId] = this.page
                    }
                    if (sessionStorage.type === 'recite') {
                        localStorage['recite_' + this.fileId] = this.page
                    }
                },

                //将现有数组中的错题提取出来
                wrongArray(array) {
                    let wrong = JSON.parse(localStorage["wrong_" + this.fileId])
                    let result = [];
                    for (let i in wrong) {
                        for (let j in array) {
                            if (wrong[i] === array[j].序号) {
                                result.push(array[j]);
                            }
                        }
                    }
                    //错题乱序返回
                    return this.randomArray(result);
                }
            },
            created() {
                this.fileName = sessionStorage.file
                this.fileId = sessionStorage.id
                if (!this.fileName) {
                    window.location.href = "./index.html"
                }
                axios.get('./json/' + this.fileName)
                    .then((response) => {
                        this.data = response.data;

                        // 如果设置了专业分类，则按专业分类筛选题目
                        if (sessionStorage.field && sessionStorage.field !== '') {
                            const field = sessionStorage.field;
                            this.data = this.data.filter(item => item.一级纲要 === field);

                            // 如果筛选后没有题目，显示提示
                            if (this.data.length === 0) {
                                this.$Message.error(`没有找到"${field}"分类的题目`);
                                // 返回选择页面
                                setTimeout(() => {
                                    window.location.href = "./type.html";
                                }, 2000);
                                return;
                            }
                        }

                        //错题模式需要筛选题目
                        if (sessionStorage.type == "wrong") {
                            //如果没有错题
                            if (localStorage["wrong_" + this.fileId] && localStorage["wrong_" + this.fileId] != "[]") {
                                this.data = this.wrongArray(this.data)
                            } else { //如果没有错题
                                this.$Message.error('您暂时无错题记录，已自动为您选择乱序答题模式');
                                sessionStorage.type = "random";
                            }
                        }
                        //乱序模式需要随机排序题目
                        if (sessionStorage.type == "random") {
                            this.data = this.randomArray(this.data)
                        }

                        if (sessionStorage.type == "order" || sessionStorage.type == "recite") {
                            this.recovery(sessionStorage.type)
                        }

                        //初始化题目
                        this.initTimu();
                    })
                    .catch(function (error) {
                        this.$Message.error('发生错误！');
                    });
            }
        })
    </script>
</body>

</html>
