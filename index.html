<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
    <meta http-equiv="Cache" content="no-cache">
    <title>刷题</title>
    <link rel="stylesheet" href="./css/iview.css">
    <link rel="stylesheet" href="./css/modern-theme.css">
    <script>
        var _hmt = _hmt || [];
        (function() {
            var hm = document.createElement("script");
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>

    <style>
        body {
            background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
            min-height: 100vh;
        }

        .title {
            text-align: center;
            margin: var(--space-xl) 0 var(--space-lg) 0;
            color: var(--text-primary);
            font-weight: 300;
            font-size: 2.5rem;
            letter-spacing: -0.5px;
        }

        .timuList {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-md);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--space-lg);
        }

        .timu {
            background: var(--primary-white);
            border: 1px solid var(--border-gray);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-light);
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .timu:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
            border-color: var(--accent-blue);
        }

        .timu .ivu-card-head {
            background: var(--soft-white);
            border-bottom: 1px solid var(--border-gray);
            padding: var(--space-lg);
        }

        .timu .ivu-card-head p {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }

        .timu .ivu-card-body {
            padding: var(--space-lg);
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .author {
            text-align: center;
            margin: var(--space-lg) 0;
            color: var(--text-muted);
            font-size: 0.8rem;
        }

        .author a {
            color: var(--accent-blue);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .author a:hover {
            color: #357abd;
        }

        /* 专业分类管理卡片特殊样式 */
        .field-manager-card {
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--soft-white) 100%);
            border: 2px dashed var(--accent-blue);
            position: relative;
            overflow: hidden;
        }

        .field-manager-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(74, 144, 226, 0.02) 50%, transparent 70%);
            pointer-events: none;
        }

        .field-manager-card:hover {
            background: linear-gradient(135deg, #f8f9ff 0%, var(--soft-white) 100%);
            border-color: #357abd;
        }

        .field-manager-card .ivu-card-head p {
            color: var(--accent-blue);
        }

        .field-manager-card .ivu-card-body p {
            margin: var(--space-xs) 0;
            font-size: 0.9rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
                margin: var(--space-lg) 0;
            }

            .timuList {
                grid-template-columns: 1fr;
                padding: 0 var(--space-sm);
            }
        }
    </style>
</head>

<body>
    <div id="el">
        <h1 class='title'>刷题</h1>
        <div class="author"><a href="">@wuxl7</a></div>
        <div class="timuList">
            <div v-for="i in jsonList" @click='goNext(i.id,i.file)'>
                <Card class="timu">
                    <p slot="title">{{i.name}}</p>
                    {{i.describe}}
                </Card>
            </div>

            <!-- 专业分类管理入口 -->
            <div @click='goToFieldManager()'>
                <Card class="timu field-manager-card">
                    <p slot="title">📚 专业分类管理</p>
                    <div>
                        <p>🎯 按一级纲要分类练习</p>
                        <p>📊 查看各分类题目统计</p>
                        <p>🚀 快速开始专项训练</p>
                    </div>
                </Card>
            </div>

        </div>
    </div>
    <script src="./js/public.js?version=1.0"></script>
    <script src="./js/vue.min.js"></script>
    <script src="./js/iview.min.js"></script>
    <script>
        const vue = new Vue({
            el: "#el",
            data: {
                jsonList: JSONList
            },
            methods: {
                goNext: function(id, file) {
                    sessionStorage.id = id;
                    sessionStorage.file = file;
                    window.location.href = "./type.html"
                },
                goToFieldManager: function() {
                    // 设置默认题库
                    if (!sessionStorage.id) {
                        sessionStorage.id = JSONList[0].id;
                        sessionStorage.file = JSONList[0].file;
                    }
                    window.location.href = "./field_manager.html"
                }
            }
        })
    </script>
</body>

</html>