<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
    <meta http-equiv="Cache" content="no-cache">
    <title>刷题</title>
    <link rel="stylesheet" href="./css/iview.css">
    <script>
        var _hmt = _hmt || [];
        (function() {
            var hm = document.createElement("script");
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>

    <style>
        .title {
            text-align: center;
            margin-top: 2vw;
        }
        
        .timuList {
            width: 96vw;
            margin: 2vw auto 0;
        }
        
        .timu {
            margin-top: 2vw;
        }
        
        .author {
            text-align: right;
            padding-right: 3.2vw;
            margin-bottom: 1.5vw;
            font-size: 7px;

        }
    </style>
</head>

<body>
    <div id="el">
        <h1 class='title'>刷题</h1>
        <div class="author"><a href="">@wuxl7</a></div>
        <div class="timuList">
            <div v-for="i in jsonList" @click='goNext(i.id,i.file)'>
                <Card class="timu">
                    <p slot="title">{{i.name}}</p>
                    {{i.describe}}
                </Card>
            </div>

            <!-- 专业分类管理入口 -->
            <div @click='goToFieldManager()'>
                <Card class="timu" style="border: 2px dashed #1890ff; background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);">
                    <p slot="title" style="color: #1890ff;">📚 专业分类管理</p>
                    <div style="color: #666;">
                        <p>🎯 按一级纲要分类练习</p>
                        <p>📊 查看各分类题目统计</p>
                        <p>🚀 快速开始专项训练</p>
                    </div>
                </Card>
            </div>

        </div>
    </div>
    <script src="./js/public.js?version=1.0"></script>
    <script src="./js/vue.min.js"></script>
    <script src="./js/iview.min.js"></script>
    <script>
        const vue = new Vue({
            el: "#el",
            data: {
                jsonList: JSONList
            },
            methods: {
                goNext: function(id, file) {
                    sessionStorage.id = id;
                    sessionStorage.file = file;
                    window.location.href = "./type.html"
                },
                goToFieldManager: function() {
                    // 设置默认题库
                    if (!sessionStorage.id) {
                        sessionStorage.id = JSONList[0].id;
                        sessionStorage.file = JSONList[0].file;
                    }
                    window.location.href = "./field_manager.html"
                }
            }
        })
    </script>
</body>

</html>