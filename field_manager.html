<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业分类管理</title>
    <link rel="stylesheet" href="./css/iview.css">
    <link rel="stylesheet" href="./css/public.css">
    <style>
        .field-manager {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .field-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .field-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .field-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #1890ff;
        }
        
        .field-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .field-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            line-height: 1.4;
            flex: 1;
            margin-right: 10px;
        }
        
        .field-count {
            background: #1890ff;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            white-space: nowrap;
        }
        
        .field-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }
        
        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.8rem;
            text-align: center;
        }
        
        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .action-btn.primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .action-btn.primary:hover {
            background: #40a9ff;
        }
        
        .search-box {
            margin-bottom: 20px;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="field-manager">
            <i-button class="back-btn" @click="goBack" type="primary" icon="ios-arrow-back">
                返回主页
            </i-button>
            
            <div class="field-stats">
                <h1>📚 专业分类管理</h1>
                <p>根据一级纲要进行专业分类，支持分类专项练习</p>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">{{totalQuestions}}</div>
                        <div>总题目数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{fieldList.length}}</div>
                        <div>专业分类数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{averageQuestions}}</div>
                        <div>平均题目数</div>
                    </div>
                </div>
            </div>
            
            <div class="search-box">
                <i-input 
                    v-model="searchKeyword" 
                    placeholder="搜索专业分类..." 
                    prefix="ios-search"
                    clearable
                    style="max-width: 400px;">
                </i-input>
            </div>
            
            <div class="field-grid">
                <div v-for="(field, index) in filteredFields" 
                     :key="index" 
                     class="field-card"
                     @click="selectField(field)">
                    
                    <div class="field-header">
                        <div class="field-name">{{field.name}}</div>
                        <div class="field-count">{{field.count}}题</div>
                    </div>
                    
                    <div class="field-actions" @click.stop>
                        <div class="action-btn primary" @click="startPractice(field.name, 'order')">
                            📖 顺序
                        </div>
                        <div class="action-btn" @click="startPractice(field.name, 'random')">
                            🔀 乱序
                        </div>
                        <div class="action-btn" @click="startPractice(field.name, 'recite')">
                            📝 背题
                        </div>
                    </div>
                </div>
            </div>
            
            <div v-if="filteredFields.length === 0 && searchKeyword" 
                 style="text-align: center; padding: 50px; color: #999;">
                <p>未找到匹配的专业分类</p>
            </div>
        </div>
    </div>

    <script src="./js/vue.min.js"></script>
    <script src="./js/iview.min.js"></script>
    <script src="./js/axios.min.js"></script>
    <script src="./js/public.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                fieldList: [],
                searchKeyword: '',
                totalQuestions: 0
            },
            computed: {
                filteredFields() {
                    if (!this.searchKeyword) {
                        return this.fieldList;
                    }
                    return this.fieldList.filter(field => 
                        field.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
                    );
                },
                averageQuestions() {
                    if (this.fieldList.length === 0) return 0;
                    return Math.round(this.totalQuestions / this.fieldList.length);
                }
            },
            created() {
                this.loadFieldData();
            },
            methods: {
                loadFieldData() {
                    const fileId = sessionStorage.id || JSONList[0].id;
                    const fileName = sessionStorage.file || JSONList[0].file;
                    
                    axios.get('./json/' + fileName)
                        .then(response => {
                            const data = response.data;
                            this.totalQuestions = data.length;
                            
                            // 统计每个分类的题目数量
                            const fieldCount = {};
                            data.forEach(item => {
                                if (item.一级纲要 && item.一级纲要.trim() !== '') {
                                    const field = item.一级纲要.trim();
                                    fieldCount[field] = (fieldCount[field] || 0) + 1;
                                }
                            });
                            
                            // 创建带数量的分类列表并排序
                            this.fieldList = Object.entries(fieldCount)
                                .map(([name, count]) => ({ name, count }))
                                .sort((a, b) => b.count - a.count);
                        })
                        .catch(error => {
                            console.error('加载数据失败:', error);
                            this.$Message.error('加载数据失败');
                        });
                },
                
                selectField(field) {
                    // 点击卡片时的默认行为（可以自定义）
                    this.$Message.info(`选择了 ${field.name} 分类`);
                },
                
                startPractice(fieldName, mode) {
                    // 设置会话存储
                    sessionStorage.field = fieldName;
                    sessionStorage.type = mode;
                    
                    const modeText = {
                        'order': '顺序答题',
                        'random': '乱序答题', 
                        'recite': '背题模式'
                    }[mode];
                    
                    this.$Message.success(`开始 ${fieldName} - ${modeText}`);
                    
                    // 跳转到答题页面
                    setTimeout(() => {
                        if (parseInt(sessionStorage.shuf_option)) {
                            window.location.href = "./timu_s.html";
                        } else {
                            window.location.href = "./timu.html";
                        }
                    }, 1000);
                },
                
                goBack() {
                    window.location.href = "./index.html";
                }
            }
        });
    </script>
</body>
</html>
