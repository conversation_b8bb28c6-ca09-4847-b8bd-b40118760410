<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业分类管理</title>
    <link rel="stylesheet" href="./css/iview.css">
    <link rel="stylesheet" href="./css/modern-theme.css">
    <style>
        body {
            background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
            min-height: 100vh;
        }

        .field-manager {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-lg);
        }

        .field-stats {
            background: linear-gradient(135deg, var(--primary-white) 0%, var(--soft-white) 100%);
            border: 1px solid var(--border-gray);
            color: var(--text-primary);
            padding: var(--space-xl);
            border-radius: var(--radius-large);
            margin-bottom: var(--space-xl);
            text-align: center;
            box-shadow: var(--shadow-light);
        }

        .field-stats h1 {
            color: var(--text-primary);
            font-weight: 300;
            font-size: 2.2rem;
            margin-bottom: var(--space-sm);
            letter-spacing: -0.5px;
        }

        .field-stats p {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: var(--space-lg);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-lg);
            margin-top: var(--space-lg);
        }

        .stat-item {
            background: var(--soft-white);
            border: 1px solid var(--border-gray);
            padding: var(--space-lg);
            border-radius: var(--radius-medium);
            transition: all 0.2s ease;
        }

        .stat-item:hover {
            background: var(--hover-bg);
            border-color: var(--accent-blue);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 300;
            color: var(--accent-blue);
            margin-bottom: var(--space-xs);
            line-height: 1;
        }

        .stat-item div:last-child {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .search-box {
            margin-bottom: var(--space-lg);
            text-align: center;
        }

        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: var(--space-lg);
            margin-top: var(--space-lg);
        }

        .field-card {
            background: var(--primary-white);
            border: 1px solid var(--border-gray);
            border-radius: var(--radius-large);
            padding: var(--space-xl);
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: var(--shadow-light);
        }

        .field-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
            border-color: var(--accent-blue);
        }

        .field-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-lg);
        }

        .field-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1.4;
            flex: 1;
            margin-right: var(--space-sm);
        }

        .field-count {
            background: var(--accent-blue);
            color: var(--primary-white);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-small);
            font-size: 0.8rem;
            font-weight: 600;
            white-space: nowrap;
        }

        .field-actions {
            display: flex;
            gap: var(--space-sm);
            margin-top: var(--space-lg);
        }

        .action-btn {
            flex: 1;
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-gray);
            background: var(--primary-white);
            border-radius: var(--radius-small);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.85rem;
            font-weight: 500;
            text-align: center;
            color: var(--text-primary);
        }

        .action-btn:hover {
            border-color: var(--accent-blue);
            color: var(--accent-blue);
            background: var(--hover-bg);
        }

        .action-btn.primary {
            background: var(--accent-blue);
            color: var(--primary-white);
            border-color: var(--accent-blue);
        }

        .action-btn.primary:hover {
            background: #357abd;
            border-color: #357abd;
        }

        .back-btn {
            position: fixed;
            top: var(--space-lg);
            left: var(--space-lg);
            z-index: 1000;
            background: var(--primary-white);
            border: 1px solid var(--border-gray);
            box-shadow: var(--shadow-medium);
        }

        .back-btn:hover {
            background: var(--hover-bg);
            border-color: var(--accent-blue);
        }

        .empty-state {
            text-align: center;
            padding: var(--space-xl);
            color: var(--text-muted);
            background: var(--soft-white);
            border: 1px solid var(--border-gray);
            border-radius: var(--radius-medium);
            margin-top: var(--space-lg);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .field-manager {
                padding: var(--space-md);
            }

            .field-stats {
                padding: var(--space-lg);
            }

            .field-stats h1 {
                font-size: 1.8rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .field-grid {
                grid-template-columns: 1fr;
                gap: var(--space-md);
            }

            .back-btn {
                top: var(--space-sm);
                left: var(--space-sm);
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="field-manager">
            <i-button class="back-btn" @click="goBack" type="primary" icon="ios-arrow-back">
                返回主页
            </i-button>
            
            <div class="field-stats">
                <h1>📚 专业分类管理</h1>
                <p>根据一级纲要进行专业分类，支持分类专项练习</p>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">{{totalQuestions}}</div>
                        <div>总题目数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{fieldList.length}}</div>
                        <div>专业分类数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{averageQuestions}}</div>
                        <div>平均题目数</div>
                    </div>
                </div>
            </div>
            
            <div class="search-box">
                <i-input 
                    v-model="searchKeyword" 
                    placeholder="搜索专业分类..." 
                    prefix="ios-search"
                    clearable
                    style="max-width: 400px;">
                </i-input>
            </div>
            
            <div class="field-grid">
                <div v-for="(field, index) in filteredFields" 
                     :key="index" 
                     class="field-card"
                     @click="selectField(field)">
                    
                    <div class="field-header">
                        <div class="field-name">{{field.name}}</div>
                        <div class="field-count">{{field.count}}题</div>
                    </div>
                    
                    <div class="field-actions" @click.stop>
                        <div class="action-btn primary" @click="startPractice(field.name, 'order')">
                            📖 顺序
                        </div>
                        <div class="action-btn" @click="startPractice(field.name, 'random')">
                            🔀 乱序
                        </div>
                        <div class="action-btn" @click="startPractice(field.name, 'recite')">
                            📝 背题
                        </div>
                    </div>
                </div>
            </div>
            
            <div v-if="filteredFields.length === 0 && searchKeyword" class="empty-state">
                <p>未找到匹配的专业分类</p>
                <p style="font-size: 0.8rem; margin-top: var(--space-sm);">请尝试其他关键词</p>
            </div>
        </div>
    </div>

    <script src="./js/vue.min.js"></script>
    <script src="./js/iview.min.js"></script>
    <script src="./js/axios.min.js"></script>
    <script src="./js/public.js"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                fieldList: [],
                searchKeyword: '',
                totalQuestions: 0
            },
            computed: {
                filteredFields() {
                    if (!this.searchKeyword) {
                        return this.fieldList;
                    }
                    return this.fieldList.filter(field => 
                        field.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
                    );
                },
                averageQuestions() {
                    if (this.fieldList.length === 0) return 0;
                    return Math.round(this.totalQuestions / this.fieldList.length);
                }
            },
            created() {
                this.loadFieldData();
            },
            methods: {
                loadFieldData() {
                    const fileId = sessionStorage.id || JSONList[0].id;
                    const fileName = sessionStorage.file || JSONList[0].file;
                    
                    axios.get('./json/' + fileName)
                        .then(response => {
                            const data = response.data;
                            this.totalQuestions = data.length;
                            
                            // 统计每个分类的题目数量
                            const fieldCount = {};
                            data.forEach(item => {
                                if (item.一级纲要 && item.一级纲要.trim() !== '') {
                                    const field = item.一级纲要.trim();
                                    fieldCount[field] = (fieldCount[field] || 0) + 1;
                                }
                            });
                            
                            // 创建带数量的分类列表并排序
                            this.fieldList = Object.entries(fieldCount)
                                .map(([name, count]) => ({ name, count }))
                                .sort((a, b) => b.count - a.count);
                        })
                        .catch(error => {
                            console.error('加载数据失败:', error);
                            this.$Message.error('加载数据失败');
                        });
                },
                
                selectField(field) {
                    // 点击卡片时的默认行为（可以自定义）
                    this.$Message.info(`选择了 ${field.name} 分类`);
                },
                
                startPractice(fieldName, mode) {
                    // 设置会话存储
                    sessionStorage.field = fieldName;
                    sessionStorage.type = mode;
                    
                    const modeText = {
                        'order': '顺序答题',
                        'random': '乱序答题', 
                        'recite': '背题模式'
                    }[mode];
                    
                    this.$Message.success(`开始 ${fieldName} - ${modeText}`);
                    
                    // 跳转到答题页面
                    setTimeout(() => {
                        if (parseInt(sessionStorage.shuf_option)) {
                            window.location.href = "./timu_s.html";
                        } else {
                            window.location.href = "./timu.html";
                        }
                    }, 1000);
                },
                
                goBack() {
                    window.location.href = "./index.html";
                }
            }
        });
    </script>
</body>
</html>
